# CNN6_30 故障识别模型

## 项目概述

CNN6_30是一个基于深度学习的故障识别系统，专门用于分析双通道时间序列信号并进行故障分类。该系统采用卷积神经网络（CNN）架构，能够自动识别和分类4种不同类型的故障模式（类别0-3）。项目支持多种模型架构选择，包括CNN模型和全连接自编码器，并集成了NNI（Neural Network Intelligence）进行自动超参数优化。

### 主要功能

- 双通道时间序列信号的故障检测与分类
- 支持多种深度学习模型架构（CNN、自编码器等）
- 自动超参数优化（基于NNI框架）
- 数据预处理（归一化、延时校正）
- 模型训练、验证和测试
- 结果可视化和性能评估

## 目录结构

```
CNN6_30/
├── main.py                     # 主程序入口
├── model.py                    # 模型定义模块
├── train.py                    # 训练模块
├── dataloader.py               # 数据加载器
├── config.yml                  # NNI配置文件
├── search_space.json           # 超参数搜索空间定义
├── nni.txt                     # NNI相关说明
├── nni1.txt                    # NNI相关说明
├── S21.py                      # S21参数处理脚本
├── DA Output Gain.png          # 增益输出图像
├── data/                       # 数据目录
│   ├── train/                  # 训练数据
│   ├── test/                   # 测试数据
│   ├── train_test/             # 训练测试数据
│   ├── train_backup/           # 训练数据备份
│   ├── test_backup/            # 测试数据备份
│   └── train_test_backup/      # 训练测试数据备份
├── model/                      # 保存的模型文件
│   └── M5_Fun10_a0.1_et0.01_W1024_U512_S[]_Data100x1024_Batch512.pt
├── picture/                    # 输出图像目录
│   └── Fun10_a0.1_et0.01_W1024_U512_S[]_Data100x1024_Batch512/
├── utils/                      # 工具模块
│   ├── __pycache__/           # Python缓存文件
│   ├── read_file.py           # 文件读取工具
│   ├── normalization.py       # 数据归一化工具
│   ├── train_test.py          # 训练测试工具
│   ├── draw.py                # 绘图工具
│   └── draw_mse.py            # MSE绘图工具
├── __pycache__/               # Python缓存文件
├── dierlie.py                 # 数据处理脚本
├── diyihang.py                # 数据处理脚本
├── diyilie.py                 # 数据处理脚本
├── fft.py                     # FFT变换脚本
├── gaiming.py                 # 重命名脚本
├── huatu.py                   # 绘图脚本
├── huatushuju.py              # 数据绘图脚本
├── quchudanwei.py             # 单位处理脚本
└── quchushijiandanwei.py      # 时间单位处理脚本
```

## 文件说明

### 核心模块

#### main.py

**主要功能**: 系统主入口，协调整个工作流程

- **关键函数**:
  - `get_params()`: 获取命令行参数和配置
  - `read_data()`: 数据读取和预处理流程
  - `test_all_data()`: 完整数据集测试
  - `main()`: 主执行函数
- **依赖关系**: 调用所有其他模块，是系统的控制中心

#### model.py

**主要功能**: 定义多种深度学习模型架构

- **主要模型类**:
  - `M5_Model`: 5层CNN模型（主要使用）
  - `M3_Model`: 3层CNN模型
  - `FCAE`: 全连接自编码器
  - `FCAE_ResNet`: ResNet型自编码器
  - `SimpleNN`: 简单神经网络
- **关键函数**:
  - `select_activation()`: 激活函数选择
- **依赖关系**: 被train.py和utils/train_test.py调用

#### train.py

**主要功能**: 模型训练核心模块

- **关键函数**:
  - `train_model()`: 主训练函数
  - `count_parameters()`: 计算模型参数数量
- **特性**:
  - 支持多GPU训练
  - 自定义损失函数
  - 学习率调度
  - 模型检查点保存
- **依赖关系**: 依赖model.py、dataloader.py、utils模块

#### dataloader.py

**主要功能**: 数据加载和预处理

- **主要类**:
  - `duan_Dataset`: 顺序采样数据集
  - `suiji_Dataset`: 随机采样数据集
- **关键函数**:
  - `create_dataloader()`: 创建PyTorch数据加载器
- **特性**:
  - 滑动窗口采样（窗口大小1024）
  - 支持两种采样策略
  - 自动标签分配（4类故障）

### 工具模块

#### utils/read_file.py

**主要功能**: 文件读取和数据合并

- **关键函数**:
  - `read_csv_file_name_list()`: 读取CSV文件列表
  - `read_merged_data()`: 合并多个CSV文件为DataFrame
- **依赖关系**: 被main.py调用

#### utils/normalization.py

**主要功能**: 数据归一化和延时校正

- **关键函数**:
  - `data_normalization()`: 主归一化函数
  - `jisuan_delay()`: 计算信号延时
  - `quchu_delay()`: 去除信号延时
  - `normalize()`: 数据归一化处理
- **特性**:
  - 支持延时自动检测和校正
  - 双通道信号归一化
- **依赖关系**: 被main.py调用

#### utils/train_test.py

**主要功能**: 模型测试和评估

- **主要类**:
  - `CustomLoss`: 自定义损失函数类（支持10种损失函数）
- **关键函数**:
  - `test_best_model()`: 最佳模型测试
  - `load_model()`: 模型加载
  - `accuracy()`: 准确率计算
  - `model_effect_visualization()`: 模型效果可视化
- **评估指标**: 准确率、召回率、F1分数
- **依赖关系**: 被main.py和train.py调用

#### utils/draw.py

**主要功能**: 数据可视化和结果绘制

- **关键函数**:
  - `draw_loss_lr()`: 绘制损失和学习率曲线
  - `data_visualization()`: 数据可视化
  - `combine_waveform_images()`: 波形图像组合
- **依赖关系**: 被main.py和train.py调用

### 配置文件

#### config.yml

**主要功能**: NNI实验配置

- **配置项**:
  - 实验名称、并发数、最大时长
  - 搜索空间文件路径
  - 调优器配置（TPE算法）
  - GPU资源配置

#### search_space.json

**主要功能**: 超参数搜索空间定义

- **主要参数**:
  - 训练参数: epochs, batch_size, learning_rate
  - 损失函数: loss_fun, loss_mul, error_tolerance
  - 数据参数: window_size, num_files
  - 模型参数: model_type, units, stage, activation

## 数据目录说明

### data/

项目的数据根目录，包含以下子目录：

- **train/**: 训练数据集

  - 包含CSV格式的时间序列数据文件
  - 每个文件包含3列：时间(T)、通道1(X)、通道2(Y)
  - 默认使用100个文件进行训练
- **test/**: 测试数据集

  - 格式与训练数据相同
  - 用于最终模型性能评估
- **train_test/**: 验证数据集

  - 训练过程中用于验证的数据
  - 用于模型选择和早停
- **train_backup/**: 训练数据备份
- **test_backup/**: 测试数据备份
- **train_test_backup/**: 验证数据备份

### 数据格式要求

- 文件格式: CSV
- 数据结构: 每3列为一组 (T, X, Y)
- T列: 时间戳
- X列: 第一通道信号数据
- Y列: 第二通道信号数据

## 模型文件说明

### model/

保存训练好的模型文件，采用PyTorch的.pt格式

### 命名规则

模型文件名格式：`{ModelType}_{Parameters}.pt`

示例：`M5_Fun10_a0.1_et0.01_W1024_U512_S[]_Data100x1024_Batch512.pt`

**参数说明**:

- `M5`: 模型类型（M5_Model）
- `Fun10`: 损失函数类型（CrossEntropyLoss）
- `a0.1`: 损失乘数
- `et0.01`: 误差容忍度
- `W1024`: 窗口大小
- `U512`: 网络单元数
- `S[]`: 阶段配置
- `Data100x1024`: 数据配置（100文件×1024样本）
- `Batch512`: 批次大小

### 模型内容

每个.pt文件包含：

- 模型状态字典 (`model`)
- 优化器状态字典 (`optimizer`)
- 损失函数参数 (`loss_fun`, `loss_mul`, `error_tolerance`)
- 最佳验证准确率 (`val_acc_max`)

## 使用方法

### 基本运行

```bash
# 直接运行（使用默认参数）
python main.py

# 使用NNI进行超参数优化
nnictl create --config config.yml
```

### 参数配置

主要参数可通过命令行或修改代码中的默认值进行配置：

```bash
python main.py --batch_size 512 --epochs 100 --window_size 1024
```

### 关键参数说明

- `--batch_size`: 批次大小（默认512）
- `--epochs`: 训练轮数（默认12）
- `--window_size`: 滑动窗口大小（默认1024）
- `--model_type`: 模型类型（M5/M3/FCAE/FCAE_ResNet/SimpleNN）
- `--num_files`: 使用的数据文件数量（默认100）
- `--loss_fun`: 损失函数类型（1-10，默认10为交叉熵）

### 训练流程

1. 数据读取和预处理
2. 数据集创建和加载
3. 模型初始化
4. 训练循环（包含验证）
5. 最佳模型保存
6. 测试集评估
7. 结果可视化

## 依赖要求

### Python版本

- Python >= 3.8

### 必需包

```
torch >= 1.8.0
torchvision >= 0.9.0
numpy >= 1.19.0
pandas >= 1.2.0
matplotlib >= 3.3.0
scikit-learn >= 0.24.0
tqdm >= 4.60.0
nni >= 2.0
natsort >= 7.1.0
pyyaml >= 5.4.0
```

### 安装依赖

```bash
pip install torch torchvision numpy pandas matplotlib scikit-learn tqdm nni natsort pyyaml
```

### 硬件要求

- **推荐**: NVIDIA GPU（支持CUDA）
- **最小**: CPU（训练速度较慢）
- **内存**: 建议8GB以上
- **存储**: 建议10GB以上可用空间

### GPU配置

项目默认使用GPU 1进行训练，可在train.py中修改：

```python
os.environ["CUDA_VISIBLE_DEVICES"] = "1"  # 修改为可用的GPU编号
```

## 输出结果

### 训练输出

- 模型文件: `model/*.pt`
- 训练曲线: `picture/*/LOSS.png`, `picture/*/acc.png`
- 控制台日志: 训练进度、损失值、准确率等

### 评估指标

- 准确率 (Accuracy)
- 召回率 (Recall)
- F1分数 (F1-Score)
- 混淆矩阵可视化

## 流图

```mermaid
graph TD
    %% 数据输入层
    A[CSV数据文件] --> A1[data/train<br/>训练数据]
    A[CSV数据文件] --> A2[data/test<br/>测试数据]
    
    %% 配置文件
    B[config.yml<br/>配置文件] --> C[main.py<br/>主程序入口]
    B1[search_space.json<br/>超参数搜索空间] --> C
    B2[NNI超参数优化] --> C
    
    %% 主程序流程
    C --> D[get_params<br/>参数获取]
    C --> E[read_data<br/>数据读取函数]
    
    %% 数据处理流水线
    E --> F[utils/read_file.py<br/>文件读取模块]
    F --> F1[read_csv_file_name_list<br/>获取CSV文件列表]
    F --> F2[read_merged_data<br/>合并CSV数据为DataFrame]
    
    F2 --> G[utils/normalization.py<br/>数据归一化模块]
    G --> G1[data_normalization<br/>信号数据归一化]
    
    G1 --> H[dataloader.py<br/>数据加载器模块]
    H --> H1[duan_Dataset<br/>顺序采样数据集]
    H --> H2[suiji_Dataset<br/>随机采样数据集]
    H1 --> H3[create_dataloader<br/>创建PyTorch数据加载器]
    H2 --> H3
    
    %% 模型定义
    I[model.py<br/>模型定义模块] --> I1[M3_Model<br/>3层CNN模型]
    I --> I2[M5_Model<br/>5层CNN模型<br/>主要使用]
    I --> I3[FCAE<br/>全连接自编码器]
    I --> I4[FCAE_ResNet<br/>ResNet型自编码器]
    I --> I5[SimpleNN<br/>简单神经网络]
    
    %% 训练流程
    H3 --> J[train.py<br/>训练模块]
    I2 --> J
    J --> J1[train_model<br/>模型训练函数]
    J1 --> J2[CustomLoss<br/>自定义损失函数]
    J1 --> J3[Adam优化器<br/>学习率调度]
    J1 --> J4[模型保存<br/>最佳验证准确率]
    
    %% 测试评估
    J4 --> K[utils/train_test.py<br/>测试评估模块]
    K --> K1[test_best_model<br/>最佳模型测试]
    K --> K2[model_effect_visualization<br/>模型效果可视化]
    K --> K3[accuracy, recall, F1-score<br/>性能指标计算]
    
    %% 可视化
    L[utils/draw.py<br/>绘图模块] --> L1[data_visualization<br/>数据可视化]
    L --> L2[draw_loss_lr<br/>损失和学习率曲线]
    L --> L3[combine_waveform_images<br/>波形图像组合]
    
    %% 输出结果
    K3 --> M[模型性能结果]
    L2 --> N[训练过程图表]
    L3 --> O[数据可视化图表]
    J4 --> P[保存的模型文件<br/>model/*.pt]
    
    %% 数据流标注
    A1 -.->|时间序列信号<br/>2通道数据| F1
    A2 -.->|时间序列信号<br/>2通道数据| F1
    H3 -.->|批次大小: 512<br/>窗口大小: 1024| J1
    I2 -.->|"4类故障分类<br/>输入: 2×1024"| J1
    
    %% 样式定义
    classDef dataFile fill:#e1f5fe
    classDef mainModule fill:#f3e5f5
    classDef utilModule fill:#e8f5e8
    classDef modelModule fill:#fff3e0
    classDef outputModule fill:#fce4ec
    
    class A,A1,A2,B,B1 dataFile
    class C,E,J,J1 mainModule
    class F,G,H,K,L utilModule
    class I,I1,I2,I3,I4,I5 modelModule
    class M,N,O,P outputModule
```

