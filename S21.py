import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 1. 读取CSV文件
input_file = 'D:/RFICtest/fangzhenqijian/LMH6881/python/CNN_分类/data/新建文件夹/1.csv'  # 替换成你的实际文件路径

df = pd.read_csv(input_file, header=None)

# 提取第一列数据作为x轴，第二列数据作为y轴
x = df.iloc[:, 0].values
y = df.iloc[:, 1].values

# 切片数据，只显示0.1MHz到1.5GHz的部分，并偏移横坐标
mask = (x >= 0.1) & (x <= 1.5)  # 选择0.1到1.5 GHz的数据范围
x_slice = x[mask]
y_slice = y[mask]

# 减小随机度
random_signal = y_slice + np.random.normal(loc=0, scale=0.6, size=len(y_slice))

# 绘制处理后的曲线（0.1到1.5 GHz）
plt.figure(figsize=(10, 6))
plt.plot(x_slice, random_signal, color='b')
plt.xlabel('Frequency (GHz)')
plt.ylabel('Magnitude (dB)')
plt.title('DA Output Gain')
plt.grid(True)

# 设置纵轴尺度范围
plt.ylim(15, 23)  # 自行根据需要调整纵轴的范围

plt.tight_layout()

# 保存图像或显示图像
plt.savefig('DA Output Gain.png')  # 保存图像
plt.show()  # 显示图像