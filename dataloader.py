from torch.utils.data import Dataset, DataLoader
import random
import numpy as np
import torch

def create_dataloader(dataset, train, batch_size):
    if train:  
        return DataLoader(dataset=dataset, batch_size=batch_size, shuffle=True,
            num_workers=1, drop_last=True, pin_memory=False)  # num_workers 通过使用多个子进程，可以加速数据加载过程
    else:  # pin_memory=True 可以帮助加速数据的传输和加载, 但会占用更多的内存空间
        return DataLoader(dataset=dataset, batch_size=batch_size, shuffle=True,
            num_workers=1, drop_last=False, pin_memory=False)  # drop_last 设置为 True 时，最后一个不完整的批次将被丢弃，不参与训练过程

class duan_Dataset(Dataset):
    def __init__(self, data, params, train, normalize_value=None):
        self.data = data
        self.N = int(data.shape[0]/3)    # 波形数量
        self.L = data.shape[1]           # 每条波形的点数
        self.window = params['window_size']             # 窗口大小
        self.num_sample = int(self.N*(self.L-self.window+1)) # 数据总共有这么多个样本
        self.train = train
        self.normalize_value = normalize_value

        if train:  # 每一段的起始点、一段取多少样本、样本的窗口间隔、是否要开启映射
            self.begin = params['duan_train_begin']
            self.num_one_line = len(self.begin) * params['num_train_sample']
        else:
            self.begin = params['duan_test_begin']
            self.num_one_line = len(self.begin) * params['num_test_sample']
            
        self.window_interval = params['window_interval']
        self.mapping_flage = params['mapping_flage']
        self.num_taken = int(self.N*self.num_one_line)    # 要取的样本数量 

        # print('........................................')
        # print('数据形状：', self.data.shape)
        # print('曲线条数：', self.N)
        # print('曲线点数：', self.L)
        # print('窗口大小：', self.window)
        # print('数据总量：', self.num_sample)
        # print('是否映射：', self.mapping_flage)
        # print('一条线的样本数量：', self.num_one_line)
        # print('数据起始点：', self.begin)
        # print('需要取的样本数量：', self.num_taken)
        # print('........................................')

        if self.num_taken > self.num_sample:
            raise ValueError('所取的样本数量大于csv文件中的样本个数')
        
        if self.mapping_flage == True:
            self.indices = self.mapping_list(self.begin)
        else: self.indices = [i for i in range(self.num_taken)]

    def __len__(self):
        return self.num_taken  # 每条线 取 num_train_sample * len(train_begin) 个样本

    def __getitem__(self, idx):
        if self.mapping_flage == True:
            idl = int(idx / self.num_one_line)  # 第几条波形
            pos = self.indices[int(idx % self.num_one_line)]  # 第几个点
        else:
            idl = int(idx / self.num_one_line)  # 第几条波形
            pos = int(idx % self.num_one_line)  # 第几个点
        
        T = self.data[3*idl, pos+self.window-1]
        if self.train:
            if idl >=0 and idl <= 24:
                y = 0
            elif idl >=25 and idl <= 49:
                y = 1
            elif idl >=50 and idl <= 74:
                y = 2
            elif idl >=75 and idl <= 99:
                y = 3
            #y = torch.tensor(y, dtype=torch.float32)  # 转换为 PyTorch 张量

        else:
            if idl ==0:
                y = 0
            elif idl ==1:
                y = 1
            elif idl ==2:
                y = 2
            elif idl ==3:
                y = 3
            y = torch.tensor(y, dtype=torch.float32)  # 转换为 PyTorch 张量
        x1 = self.data[3*idl+1, pos:(pos+self.window)]  # 获取第二列的数据
        x2 = self.data[3*idl+2, pos:(pos+self.window)]  # 获取第三列的数据
        x1 = x1.reshape(1, -1)
        x2 = x2.reshape(1, -1)  # 一维变二维，1 行 window 列
        x = np.concatenate((x1, x2), axis=1) # 1 行 2*window 列
        x = x.reshape(2, -1)
        x = torch.tensor(x, dtype=torch.float32)  # 将 NumPy 数组转换为 PyTorch 张量
        return T, x, y, idl
    
    def mapping_list(self, lst):
        mapped_list = [i for begin in lst for i in range(begin, begin+self.window_interval*self.num_one_line, self.window_interval)]
        #print(mapped_list)  # 从 begin 开始，每间隔 window_interval 个位置取一个数，共取 num_one_line 个数
        return mapped_list

class suiji_Dataset(Dataset):
    def __init__(self, data, params, train, normalize_value=None):
        self.data = data
        self.N = int(data.shape[0]/3)    # 波形数量
        self.L = data.shape[1]           # 每条波形的点数
        self.window = params['window_size']             # 窗口大小
        self.num_one_line = int(self.L-self.window+1)        # 每条曲线最多取的样本数量
        self.num_sample = int(self.N*(self.L-self.window+1)) # 数据总共有这么多个样本
        self.normalize_value = normalize_value

        if train:
            self.num_taken = int(self.N * params['suiji_train_num'])      # 要取的样本数量
        else: self.num_taken = int(self.N * params['suiji_test_num'])        
 
        self.mapping_flage = params['mapping_flage']

        # print('........................................')
        # print('数据形状：', self.data.shape)
        # print('曲线条数：', self.N)
        # print('曲线点数：', self.L)
        # print('窗口大小：', self.window)
        # print('数据总量：', self.num_sample)
        # print('是否映射：', self.mapping_flage)
        # print('一条线的样本数量：', self.num_one_line)
        # print('需要取的样本数量：', self.num_taken)
        # print('........................................')

        if self.num_taken > self.num_sample:
            raise ValueError('所取的样本数量大于csv文件中的样本个数')
        
        if self.mapping_flage == True:
            self.indices = self.mapping_list()
        else: self.indices = [i for i in range(self.num_taken)]

    def __len__(self):
        return self.num_taken  # 要取的样本数量

    def __getitem__(self, idx):
        if self.mapping_flage == True:
            index = self.indices[idx]  # 从总数据的量中随机映射出需要的样本数量的索引
        else: index = idx
        idl = int(index / self.num_one_line)  # 第几条波形
        pos = int(index % self.num_one_line)  # 第几个点


        T = self.data[3*idl, pos+self.window-1]
        if idl >=0 and idl <= 24:
            y = 0
        elif idl >=25 and idl <= 49:
            y = 1
        elif idl >=50 and idl <= 74:
            y = 2
        elif idl >=75 and idl <= 99:
            y = 3
        #y = torch.tensor(y, dtype=torch.float32)  # 转换为 PyTorch 张量
        x1 = self.data[3*idl+1, pos:(pos+self.window)]  # 获取第二列的数据
        x2 = self.data[3*idl+2, pos:(pos+self.window)]  # 获取第三列的数据
        x1 = x1.reshape(1, -1)
        x2 = x2.reshape(1, -1)  # 一维变二维，1 行 window 列
        x = np.concatenate((x1, x2), axis=1) # 1 行 2*window 列
        x = x.reshape(2, -1)
        x = torch.tensor(x, dtype=torch.float32)  # 将 NumPy 数组转换为 PyTorch 张量
        return T, x, y, idl
    
    def mapping_list(self):
        all_numbers = list(range(self.num_sample)) # 生成包含从 0 到 num_sample-1 的所有数的列表
        mapped_list = random.sample(all_numbers, self.num_taken) # 从列表中随机抽取num_taken个不同的数
        return mapped_list


if __name__ == '__main__':
    train_data = np.array([[j+i*100 for j in range(100)] for i in range(27)])
    test_data = np.array([[j+i*100 for j in range(100)] for i in range(3)])

    shuju = {'normal_flage': True, 'yanchi_flage': True, 'delay_known': True, 'delay_dian': 165,
             'train': True, 'normal_min': 1, 'mormal_max': 1}
    
    wangluo = {'window_size':3, 'uints':[512, 256, 128, 64], 'batch_size': 512, 'epochs': 100,
                'dropout': None, 'loss_fun': 10, 'loss_mul' :10}  # 损失函数选择  1:MSE  2:MAE  3:MAE_MAX  4:MSE+loss_mul*MAE_MAX^2
    
    params = {'mapping_flage': True, 'train_begin': [0], 'test_begin': [0], 
                   'window_interval': 3, 'num_look': 5, 'window_size':3, 'duan_train_begin': [0], 'duan_test_begin': [0],
                   'num_train_sample': 5, 'num_test_sample': 10}
    num_train_sample = len(params['train_begin']) * params['num_train_sample']
    num_test_sample = len(params['test_begin']) * params['num_test_sample']
    params['num_train_sample'] = num_train_sample
    params['num_test_sample'] = num_test_sample

    train_normalize_value = params.get('train_normalize_value', None)
    train_dataset = duan_Dataset(train_data, params, shuju['train'], None)
    test_dataset = duan_Dataset(test_data, params, not shuju['train'], normalize_value=train_normalize_value)
    train_loader = create_dataloader(train_dataset, shuju['train'], wangluo['batch_size'])
    test_loader = create_dataloader(test_dataset, not shuju['train'], wangluo['batch_size'])

    print('\n批次大小:', wangluo['batch_size'])  
    print('训练样本总数:', len(train_dataset))
    print('训练批次数:', len(train_loader))
    print('测试样本总数:', len(test_dataset))
    print('测试批次数:', len(test_loader),'\n')

    for Txy_data in train_loader:  # 取一个 batch_size 的数据
        x = Txy_data[1].view(-1, wangluo['window_size']) # (batch_size, window_size)
        y = Txy_data[2].view(-1, 1)

    print('................')

    for Txy_data in test_loader:  # 取一个 batch_size 的数据
        x = Txy_data[1].view(-1, wangluo['window_size']) # (batch_size, window_size)
        y = Txy_data[2].view(-1, 1)   
