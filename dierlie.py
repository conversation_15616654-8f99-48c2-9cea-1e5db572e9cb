import pandas as pd
import numpy as np

# 1. 读取CSV文件
input_file = 'D:/RFICtest/fangzhenqijian/LMH6881/python/CNN_分类/data/新建文件夹/2.csv'  # 替换成你的实际文件路径
output_file = 'D:/RFICtest/fangzhenqijian/LMH6881/python/CNN_分类/data/新建文件夹/22.csv'

df = pd.read_csv(input_file)

# 2. 处理第二列（假设第二列数据格式为Mag/Deg）
def mag_deg_to_db(mag_deg):
    mag, deg = mag_deg.split(' / ')
    mag_db = 20 * np.log10(float(mag))
    return f"{mag_db:.2f} dB"

df.iloc[:, 1] = df.iloc[:, 1].apply(mag_deg_to_db)

# 3. 保存修改后的CSV文件
df.to_csv(output_file, index=False)

print(f"处理完成并保存到 {output_file}")
