import os
import pandas as pd

def modify_csv_headers(folder_path):
    # 获取文件夹中的所有文件名
    files = os.listdir(folder_path)
    
    # 遍历文件夹中的所有文件
    for file_name in files:
        # 检查文件是否为 CSV 文件
        if file_name.endswith('.csv'):
            file_path = os.path.join(folder_path, file_name)
            
            # 读取 CSV 文件
            df = pd.read_csv(file_path)
            
            # 修改第一行的前三列
            df.columns.values[0] = 'T'
            df.columns.values[1] = 'X'
            df.columns.values[2] = 'Y'
            
            # 将修改后的 DataFrame 写回到 CSV 文件中
            df.to_csv(file_path, index=False)

# 使用示例
folder_path = 'D:/RFICtest/fangzhenqijian/LMH6881/6881_4/LMH6881_ADS_AIO/LMH6881_wrk/random/a'
modify_csv_headers(folder_path)
