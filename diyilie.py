import pandas as pd

# 1. 读取CSV文件
input_file = 'D:/RFICtest/fangzhenqijian/LMH6881/python/CNN_分类/data/新建文件夹/2.csv'  # 替换成你的实际文件路径
output_file = 'D:/RFICtest/fangzhenqijian/LMH6881/python/CNN_分类/data/新建文件夹/22.csv'

# 2. 跳过第一行，读取剩余数据
df = pd.read_csv(input_file, skiprows=1, header=None)

# 3. 处理第一列的前900行
df.iloc[:9000, 0] = df.iloc[:9000, 0] / 1000  # 将前900行数据除以1000

# 4. 保存修改后的CSV文件
df.to_csv(output_file, index=False, header=False)

print(f"处理完成并保存到 {output_file}")
