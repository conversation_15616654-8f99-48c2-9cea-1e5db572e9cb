import os
from normalization import data_normalization
from utils.read_file import read_merged_data, read_csv_file_name_list
from draw import data_visualization, data_fft

if __name__ == '__main__':

    # 定义参数 1024 2048 4096 8192 16384 32768 65536 131072
    uints = [2048, 1024, 512, 256, 128, 64, 32, 16]
    window_size = 4096; batch_size = 128; epochs = 500
    max_lr = 1e-4; min_lr = 5e-6; gamma = 0.95; att_epoch = 5
    min_value = -1; max_value = 1;     # 归一化范围、是否归一化
    normal_flage = True; train = True; yanchi_flage =  True  
    train_per_wave_n = 9; test_per_wave_n = 1
    train_after_wave_n = 9; test_after_wave_n = 1   # 训练前后，看多少条波形
    control_length = 1

    train_path = r'./data/train'
    test_path = r'./data/test'   # 指定文件夹路径
    pt_dir = r'./model'   # 调用 checkpoint

    # 读取文件并归一化数据
    '''
    train_path = r'/home/<USER>/project/CNN/CNN_waveform/data/train'
    test_path = r'/home/<USER>/project/CNN/CNN_waveform/data/test'   # 指定文件夹路径
    pt_dir = r'/home/<USER>/project/CNN/CNN_waveform/model'   # 调用 checkpoint
    '''
    model_name = 'FCAE'
    number = '_' + str(control_length)
    checkpoint_path = os.path.join(pt_dir, str(model_name) + number + '.pt')
    
    train_csv_list = read_csv_file_name_list(train_path)  
    train_df, train_N, train_L = read_merged_data(train_csv_list, train) # 得到 DataFram 的训练数据，波形条数，波形点数

    test_csv_list = read_csv_file_name_list(test_path)
    test_df, test_N, test_L = read_merged_data(test_csv_list, not train) # 得到 DataFram 的测试数据，波形条数，波形点数

    if control_length == 1:  # 0.97w
        train_L = 5000 + window_size -1; test_L = 5000 + window_size -1
        train_df = train_df.iloc[:train_L, :]
        test_df = test_df.iloc[:test_L, :]
        window_size = 1024; batch_size = 512
        max_lr = 1e-4; min_lr = 1e-5; gamma = 0.95; att_epoch = 5
        
    if control_length == 2:  # 18.8w
        train_L = 20000; test_L = 1500
        train_df = train_df.iloc[:train_L, :]
        test_df = test_df.iloc[:test_L, :]
        window_size = 1024; batch_size = 8192
        max_lr = 2e-5; min_lr = 1e-5; gamma = 0.95; att_epoch = 5
           
    if control_length == 3:  # 98.8w
        train_L = 100000; test_L = 1500
        train_df = train_df.iloc[:train_L, :]
        test_df = test_df.iloc[:test_L, :]
        window_size = 1024; batch_size = 8192
        max_lr = 1e-4; min_lr = 1e-5; gamma = 0.95; att_epoch = 5

    train_data, train_delay, train_y_min, train_y_max = data_normalization(train_df, train_N, train_L, min_value, max_value, normal_flage, yanchi_flage, train)
    print(type(train_data), train_data.shape, 'train_delay:', train_delay, '\n')

    test_data, test_delay, test_y_min, test_y_max = data_normalization(test_df, test_N, test_L, min_value, max_value, normal_flage, not yanchi_flage, not train)      
    print(type(test_data), test_data.shape, 'test_delay:', test_delay, '\n')  # train_data 类型是 numpy, shape = (3*N, L-delay)
    print('data归一化完成') 

    data_fft(train_data, train_per_wave_n, train)
    data_fft(test_data, test_per_wave_n, not train)
