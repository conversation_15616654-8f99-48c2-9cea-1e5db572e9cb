import os

# 目标文件夹路径
folder_path = 'D:/RFICtest/fangzhenqijian/LMH6881/6881_4/LMH6881_ADS_AIO/LMH6881_wrk/random/Input_open'

# 遍历目标文件夹中的所有文件
for filename in os.listdir(folder_path):
    # 检查文件是否是CSV文件并且名称是否以"updated_"开头
    if filename.endswith('.csv') and filename.startswith('updated_'):
        # 提取数字部分
        new_name = filename.replace('updated_', '')
        # 构建旧文件路径和新文件路径
        old_file_path = os.path.join(folder_path, filename)
        new_file_path = os.path.join(folder_path, new_name)
        # 重命名文件
        os.rename(old_file_path, new_file_path)
        print(f'Renamed: {old_file_path} to {new_file_path}')

print('所有文件重命名完成。')
