import os
import pandas as pd
import matplotlib.pyplot as plt

# 目标文件夹路径
folder_path = 'D:/RFICtest/fangzhenqijian/LMH6881/6881_4/LMH6881_ADS_AIO/LMH6881_wrk/random/a'

# 获取文件夹内所有CSV文件的路径
csv_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.csv')]

# 初始化绘图
plt.figure(figsize=(12, 8))

# 逐个读取CSV文件并绘制曲线
for csv_file in csv_files:
    # 读取CSV文件
    data = pd.read_csv(csv_file)
    
    # 检查数据是否足够长
    if len(data) < 10000:
        print(f"文件 {csv_file} 不足10000行，跳过")
        continue
    
    # 从第150行到第10000行截取数据
    data_section = data.iloc[150:10000]
    
    # 绘制输入X的数据
    plt.plot(data_section.iloc[:, 0], data_section.iloc[:, 1], label=f'{os.path.basename(csv_file)} - X')
    # 绘制输出Y的数据
    plt.plot(data_section.iloc[:, 0], data_section.iloc[:, 2], label=f'{os.path.basename(csv_file)} - Y')

# 添加图例
plt.legend()
# 添加标题和标签
plt.title('CSV Files Data from 150th to 10000th Point')
plt.xlabel('Time ns')
plt.ylabel('Voltage mv')
# 显示图形
plt.show()
