import matplotlib.pyplot as plt

# 参数
metrics = ['Accuracy', 'Recall', 'F1 Score']
values = [0.98388671875, 0.98388671875, 0.9838699668826438]

# 创建折线图
plt.plot(metrics, values, marker='o', color='blue', linestyle='-', linewidth=2)

# 添加标题和标签
plt.title('Model Performance Metrics')
plt.xlabel('Metrics')
plt.ylabel('Scores')

# 设置y轴范围为0.98到1.0
plt.ylim(0.98, 1.0)

# 显示数值，精确到小数点后16位
for i, v in enumerate(values):
    plt.text(i, v + 0.0002, f"{v:.16f}", ha='center')

# 显示图形
plt.grid()
plt.show()
