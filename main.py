import os
import argparse
import nni
from dataloader import duan_Dataset, suiji_Dataset, create_dataloader
from train import train_model 
from utils.train_test import test_best_model
from utils.normalization import data_normalization
from utils.read_file import read_merged_data, read_csv_file_name_list
from utils.draw import data_visualization,combine_waveform_images
import gc
gc.disable()   # 禁用 Tkinter 的自动垃圾回收机制，从而避免模型训练时报关于 Tkinter 的错

def get_params():   # Get parameters from command line 
    parser = argparse.ArgumentParser()    
    parser.add_argument("--testing_in_my_computer", type=bool, default=True, help="testing in my computer flag")
    parser.add_argument("--project_path", type=str, default='/home/<USER>/fcnn/random/', help="fuwuqi project path")
    parser.add_argument("--normal_flage", type=bool, default=True, help="normal flag")
    parser.add_argument("--train", type=bool, default=True, help="train flag")
    parser.add_argument("--yanchi_flage", type=bool, default=True, help="yanchi flag")
    parser.add_argument("--delay_known", type=bool, default=True, help="delay known flag")
    parser.add_argument("--train_delay", type=list, default=[10], help="train delay")
    parser.add_argument("--test_delay", type=list, default=[10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10], help="test delay")
    parser.add_argument("--picture_save_path", type=str, default=None, help="picture_save_path")

    parser.add_argument("--num_files", type=int, default=100, help="num_files")
    parser.add_argument("--train_per_wave_n", type=int, default=1, help="train per wave")
    parser.add_argument("--test_per_wave_n", type=int, default=4, help="test per wave")
    parser.add_argument("--train_after_wave_n", type=int, default=1, help="train after wave")
    parser.add_argument("--test_after_wave_n", type=int, default=4, help="test after wave")

    parser.add_argument("--data_suiji_flage", type=int, default=1, help="dataset_xuanze")
    parser.add_argument("--suiji_train_num", type=int, default=1024, help="train begin list")
    parser.add_argument("--suiji_test_num", type=int, default=1024, help="test begin list")
    parser.add_argument("--mapping_flage", type=bool, default=True, help="mapping flag")
    parser.add_argument("--window_interval", type=int, default=1, help="window interval")
    parser.add_argument("--num_look", type=int, default=1024, help="number of look")
    parser.add_argument("--duan_train_begin", type=list, default=[150], help="train begin list")
    parser.add_argument("--duan_test_begin", type=list, default=[150], help="test begin list")
    parser.add_argument("--num_train_sample", type=int, default=1024, help="number of train samples")
    parser.add_argument("--num_test_sample", type=int, default=1024, help="number of test samples")

    parser.add_argument("--batch_size", type=int, default=512, help="batch size")
    parser.add_argument("--epochs", type=int, default=12, help="number of epochs")
    parser.add_argument("--loss_fun", type=int, default=10, help="loss function")  
    parser.add_argument("--loss_mul", type=float, default=0.1, help="loss multiplier")
    parser.add_argument("--error_tolerance", type=float, default=0.01, help="error_tolerance")
    parser.add_argument("--max_lr", type=float, default=1e-5, help="maximum learning rate")
    parser.add_argument("--min_lr", type=float, default=5e-6, help="minimum learning rate")
    parser.add_argument("--gamma", type=float, default=0.95, help="gamma value")
    parser.add_argument("--att_epoch", type=int, default=20, help="attention epoch")

    parser.add_argument("--window_size", type=int, default=1024, help="window size")
    parser.add_argument("--activation", type=str, default="relu", help="select_activation")
    parser.add_argument("--stage", type=list, default=[], help="stage")
    parser.add_argument("--units", type=list, default=[512], help="units")
    parser.add_argument("--model_type", type=str, default='M5', help="model_type")
    parser.add_argument("--ed_pyramid", type=int, default=0, help="ed_pyramid")
    parser.add_argument("--stride", type=int, default=2, help="stride")
    
    args, _ = parser.parse_known_args()
    return args

def read_data(params):
    params['test_delay'] = [10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10]
    if params['testing_in_my_computer']:  # 读取文件
        train_file_path = r'./data/train'
        val_file_path = r'./data/train_test'
        test_file_path = r'./data/test'
    else:
        train_file_path = params['project_path'] + '/data/train'
        test_file_path = params['project_path'] + '/data/test'

    train_files = read_csv_file_name_list(train_file_path, params['train'], params['num_files']) 
    val_files = read_csv_file_name_list(test_file_path, not params['train'], params['test_per_wave_n']) 
    train_df = read_merged_data(train_files, params['train']) # 得到 DataFram 的训练数据
    test_df = read_merged_data(val_files, not params['train']) # 得到 DataFram 的测试数据
    # print('test_df:', test_df)

    # 归一化曲线
    train_data, train_normalize_value = data_normalization(train_df, params['train'], params, None)
    test_data, test_normalize_value = data_normalization(test_df, not params['train'], params, train_normalize_value)      
    print('训练数据：', train_data.shape, '测试数据：', test_data.shape)  # data 类型是 numpy, shape = (3*N, L-delay)
    print('data 归一化完成') 
    
    if params['data_suiji_flage'] == 1:  # 用 DataLoader 加载数据
        train_dataset = suiji_Dataset(train_data, params, params['train'])
        #test_dataset = suiji_Dataset(test_data, params, not params['train'])
        test_dataset = duan_Dataset(test_data, params, not params['train'])
        print('随机取样')
    else:
        train_dataset = duan_Dataset(train_data, params, params['train'])
        test_dataset = duan_Dataset(test_data, params, not params['train'])
        print('成段取样')

    train_loader = create_dataloader(train_dataset, params['train'], params['batch_size'])
    test_loader = create_dataloader(test_dataset, not params['train'], params['batch_size'])
    print('dataloader 完成\n')

    print('批次大小:', params['batch_size'])  
    print('训练样本总数:', len(train_dataset))
    print('训练批次数:', len(train_loader))
    print('测试样本总数:', len(test_dataset))
    print('测试批次数:', len(test_loader),'\n')

    return train_data, test_data, train_loader, test_loader



def test_all_data(params, checkpoint_path, model_name, model_number):
    print('\n..................测试所有曲线......................')
    params['test_delay'] = [10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10]
    if params['testing_in_my_computer']:  # 读取文件
        train_file_path = r'./data/train'
        test_file_path = r'./data/test'
    else:
        train_file_path = params['project_path'] + '/data/train'
        test_file_path = params['project_path'] + '/data/test'

    train_files = read_csv_file_name_list(train_file_path, params['train'], params['num_files']) 
    test_files = read_csv_file_name_list(test_file_path, not params['train'], params['num_files']) 
    train_df = read_merged_data(train_files, params['train']) # 得到 DataFram 的训练数据
    test_df = read_merged_data(test_files, not params['train']) # 得到 DataFram 的测试数据
    train_data, train_normalize_value = data_normalization(train_df, params['train'], params, None)
    test_data, test_normalize_value = data_normalization(test_df, not params['train'], params, train_normalize_value)    
    print('训练数据：', train_data.shape, '测试数据：', test_data.shape)  
    print('data 归一化完成') 

    test_best_model(checkpoint_path, train_data, test_data,
                model_name, model_number, params)

def main(params):
    train_data, test_data, train_loader, test_loader = read_data(params)
    model_name = params['model_type']

    if model_name == 'FCAE':
        model_structure = 'Fun'+str(params['loss_fun'])+'_a'+str(params['loss_mul'])+'_et'+str(params['error_tolerance'])+\
                           '_W'+str(params['window_size'])+'_U'+str(params['units'][0])+'_L'+str(len(params['units']))
    else:
        model_structure = 'Fun'+str(params['loss_fun'])+'_a'+str(params['loss_mul'])+'_et'+str(params['error_tolerance'])+\
                           '_W'+str(params['window_size'])+'_U'+str(params['units'][0])+'_S'+str(params['stage'])
    
    data_structure = '_Data'+str(params['num_files'])+'x'+str(params['suiji_train_num'])+'_Batch'+str(params['batch_size'])
    model_number = model_structure + data_structure
    # 例如：FCAE_Fun1_W1024_U512_L4_data100x1024_Batch512

    # "units": {"_type":"choice", "_value":[[512,256,128,64], [512,256,128,64,32], [512,256,128,64,32,16],
    # [1024,512,256,128], [1024,512,256,128,64], [1024,512,256,128,64,32], [1024,512,256,128,64,32,16],
    # [2048,1024,512,256], [2048,1024,512,256,128], [2048,1024,512,256,128,64], [2048,1024,512,256,128,64,32], [2048,1024,512,256,128,64,32,16],
    # [4096,2048,1024,512], [4096,2048,1024,512,256], [4096,2048,1024,512,256,128], [4096,2048,1024,512,256,128,64], [4096,2048,1024,512,256,128,64,32], [4096,2048,1024,512,256,128,64,32,64]]},
    
    if params['testing_in_my_computer']:
        pt_dir = './model'   # 调用 checkpoint
        picture_dir = './picture' 
    else:
        pt_dir = params['project_path'] + '/model'   # 调用 checkpoint
        picture_dir = params['project_path'] + '/picture'
    
    picture_save_dir = os.path.join(picture_dir, model_number)
    os.makedirs(picture_save_dir, exist_ok=True)
    os.makedirs(pt_dir, exist_ok=True)
    params['picture_save_path'] = picture_save_dir  # 设置图片保存路径 
    checkpoint_path = os.path.join(pt_dir, str(model_name) + '_' + model_number + '.pt')
    print('picture save in:', picture_save_dir)
    print('model save in:', checkpoint_path, '\n')

    data_visualization(train_data, params['train'], params)   # 数据可视化
    data_visualization(test_data, not params['train'], params)

    combine_waveform_images(test_data, params)

    train_model(params, train_loader, test_loader,
                checkpoint_path, model_name, model_number) # 保存训练过程中验证损失最低的model 

    test_all_data(params, checkpoint_path, model_name, model_number)

if __name__ == '__main__':

    tuner_params = nni.get_next_parameter()  # get parameters form tuner
    params = vars(get_params())   # get parameters form orign_params
    params.update(tuner_params)   # 合并两个列表，将params中与tuner挑选出的组合合并
    main(params)
