import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(device)


class M3_Model(nn.Module):
    def __init__(self):
        super(M3_Model,self).__init__()
        self.conv1 = nn.Conv1d(2,256,80,4, padding=38)
        self.batchnorm1 = nn.BatchNorm1d(256)
        self.maxpool1 = nn.MaxPool1d(4)
        
        self.conv2 = nn.Conv1d(256,256,3, padding=1)
        self.batcnorm2 = nn.BatchNorm1d(256)
        self.maxpool2 = nn.MaxPool1d(4)
        
        self.avgpool = nn.AdaptiveAvgPool1d(1)
        self.FC = nn.Linear(256,4)
        
    def forward(self,x):
        x = self.conv1(x)
        x = F.relu(self.batchnorm1(x))
        x = self.maxpool1(x)
        x = self.conv2(x)
        x = F.relu(self.batcnorm2(x))
        x = self.maxpool2(x)
        x = self.avgpool(x)
        x = x.view(-1,256)
        x = self.FC(x)
        #x = F.log_softmax(x, dim=1)
        return x
    
class M5_Model(nn.Module):
    def __init__(self):
        super(M5_Model, self).__init__()
        self.c = 128
        self.conv1 = nn.Conv1d(2, self.c, 80, 4, padding=38)
        self.batchnorm1 = nn.BatchNorm1d(self.c)
        self.maxpool1 = nn.MaxPool1d(4)
        
        self.conv2 = nn.Conv1d(self.c, 2*self.c, 3, padding=1)
        self.batchnorm2 = nn.BatchNorm1d(2*self.c)
        self.maxpool2 = nn.MaxPool1d(4)
        
        self.conv3 = nn.Conv1d(2*self.c, 4*self.c, 3, padding=1)
        self.batchnorm3 = nn.BatchNorm1d(4*self.c)
        self.maxpool3 = nn.MaxPool1d(4)
        
        self.conv4 = nn.Conv1d(4*self.c, 8*self.c, 3, padding=1)
        self.batchnorm4 = nn.BatchNorm1d(8*self.c)
        self.maxpool4 = nn.MaxPool1d(2)  
        
        self.avgpool = nn.AdaptiveAvgPool1d(2)  
        self.FC = nn.Linear(8*self.c * 2, 4)  

    def forward(self, x):

        x = self.conv1(x)
        x = F.relu(self.batchnorm1(x))
        
        x = self.maxpool1(x)
        x = self.conv2(x)
        x = F.relu(self.batchnorm2(x))
        
        x = self.maxpool2(x)
        x = self.conv3(x)
        x = F.relu(self.batchnorm3(x))
        
        x = self.maxpool3(x)
        x = self.conv4(x)
        x = F.relu(self.batchnorm4(x))
        
        x = self.maxpool4(x)
        x = self.avgpool(x)
        
        x = x.view(-1, 8*self.c * 2)
        x = self.FC(x)
        return x


class BasicBlock(nn.Module):
    def __init__(self, in_planes, planes, activation, downsample=None):
        super(BasicBlock, self).__init__()
        self.fc1 = nn.Linear(in_planes, planes)
        self.bn1 = nn.BatchNorm1d(planes)
        self.fc2 = nn.Linear(planes, planes)
        self.bn2 = nn.BatchNorm1d(planes)
        self.activation = select_activation(activation)
        self.downsample = downsample

    def forward(self, x):
        identity = x
        out = self.fc1(x)
        out = self.bn1(out)
        out = self.activation(out)
        out = self.fc2(out)
        out = self.bn2(out)
        if self.downsample is not None:
            identity = self.downsample(x)
        out += identity
        out = self.activation(out)
        return out
    
class SimpleNN(nn.Module):
    def __init__(self, input_size, num_classes):
        super(SimpleNN, self).__init__()
        self.fc1 = nn.Linear(input_size, 50)
        self.fc2 = nn.Linear(50, num_classes)
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight.data) 
    
    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = self.fc2(x)
        return x

class FCAE_ResNet(nn.Module):   # 定义 ResNet 型自动编码器类
    def __init__(self, block=BasicBlock, input_dim=1024, stage=[2,2,2,2], stride=2, activation='tanh', train=True, ed_pyramid=1):
        super(FCAE_ResNet, self).__init__()
        self.input_dim = input_dim
        self.stage = stage
        self.stride = stride
        self.in_first_block_shape = int(self.input_dim/self.stride) # 数据进入第一个block时的形状
        self.train_flage = train
        self.activation = select_activation(activation)
        self.ed_pyramid = ed_pyramid   # AE 或是 金字塔
        self.layer_list = []           # 保存每个 stage  
        self.data_shape = []           # 保存信息

        self.data_shape.append(self.input_dim)
        self.fc_in = nn.Linear(self.input_dim, self.in_first_block_shape)
        self.data_shape.append(self.in_first_block_shape)
        self.bn1 = nn.BatchNorm1d(self.data_shape[-1])

        for i in range(len(self.stage)):
            if i == 0:
                self.layer_list.append(self._make_layer(block, self.stage[i], self.data_shape[-1], stride=1)) # 第一个stage不变形状
            else:
                self.layer_list.append(self._make_layer(block, self.stage[i], self.data_shape[-1], stride=self.stride)) # 其后每个stage缩减stride倍
                self.data_shape.append(int(self.data_shape[-1]/self.stride))
        self.fc_out = nn.Linear(self.data_shape[-1], 1) # 金字塔型直接输出

        if self.ed_pyramid == 1: # 如果是自编码器对称结构
            for i in range(len(self.stage)-1, 0, -1):
                self.layer_list.append(self._make_layer(block, self.stage[i], self.data_shape[-1], stride=(1/self.stride)))
                self.data_shape.append(int(self.data_shape[-1]*self.stride))
            self.fc_out = nn.Linear(self.data_shape[-1], 1)

        self.layer_list = nn.Sequential(*self.layer_list)

        if self.train_flage: self.print_info()
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.normal_(module.weight.data) 
            elif isinstance(module, nn.BatchNorm1d):
                nn.init.constant_(module.weight, 1)  # 初始化 self.bn1
                nn.init.constant_(module.bias, 0)  
            if isinstance(module, BasicBlock):           # 使得残差分支以零开始，并且每个残差块表现得像一个恒等式
                nn.init.constant_(module.bn2.weight, 0)  # 用于提高模型的训练稳定性和收敛速度
        
    def _make_layer(self, block_type, block_num, stage_in_shape, stride=1):
        downsample = None; out_shape = int(stage_in_shape/stride) # 根据stage_in_shape/stride来确定输出维度
        if stride != 1 or self.in_first_block_shape != stage_in_shape:
            downsample = nn.Sequential(nn.Linear(stage_in_shape, out_shape), nn.BatchNorm1d(out_shape))
        layers = []
        layers.append(block_type(stage_in_shape, out_shape, self.activation, downsample))
        self.in_first_block_shape = out_shape
        for _ in range(1, block_num):  # 一个 block 中重复几个 BasicBlock/Bottleneck
            layers.append(block_type(out_shape, out_shape, self.activation, downsample=None))
        return nn.Sequential(*layers)

    def forward(self, x):
        x = self.fc_in(x)                # 输入层
        x = self.bn1(x)
        x = self.activation(x)
        for i in range(len(self.stage)): # 编码部分
            x = self.layer_list[i](x) 
        if self.ed_pyramid == 1:         # 解码部分
            for i in range(len(self.stage), 2*len(self.stage)-1):
                x = self.layer_list[i](x)
        x = torch.tanh(self.fc_out(x))   # 输出层
        return x

    def print_info(self):
        x = torch.randn(32, self.input_dim)
        print('in_dim\t', x.shape)
        x = self.fc_in(x)
        print('fc_in\t', x.shape)
        for i in range(len(self.stage)):
            x = self.layer_list[i](x) 
            print('stage_'+str(i)+':', x.shape)
        if self.ed_pyramid == 1:
            for i in range(len(self.stage), 2*len(self.stage)-1):
                x = self.layer_list[i](x)
                print('stage_'+str(i)+':', x.shape)
        x = self.fc_out(x)
        print('out_dim\t', x.shape)


class FCAE(nn.Module):
    def __init__(self, input_dim=1024, units=[512, 256, 128, 64], activation='rlu', train=True, ed_pyramid=0, dropout_rate=0.5):
        super(FCAE, self).__init__()
        self.input_dim = 2 * input_dim
        self.units = units
        self.activation = activation
        self.ed_pyramid = ed_pyramid
        self.train_flag = train
        self.dropout_rate = dropout_rate
        self.encoder = self.build_encoder()
        if self.ed_pyramid == 1:
            self.decoder = self.build_decoder()
        self.fc_out = self.build_out()

        if self.train_flag:
            self.print_info()
        
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.kaiming_normal_(module.weight.data)

    def build_encoder(self):
        encoder_layers = []
        encoder_layers.append(nn.Linear(self.input_dim, self.units[0]))
        encoder_layers.append(select_activation(self.activation))
        encoder_layers.append(nn.Dropout(self.dropout_rate))
        for i in range(1, len(self.units)):
            encoder_layers.append(nn.Linear(self.units[i - 1], self.units[i]))
            encoder_layers.append(select_activation(self.activation))
            encoder_layers.append(nn.Dropout(self.dropout_rate))
        encoder = nn.Sequential(*encoder_layers)
        return encoder

    def build_decoder(self):
        decoder_layers = []
        for i in range(len(self.units) - 1, 0, -1):
            decoder_layers.append(nn.Linear(self.units[i], self.units[i - 1]))
            decoder_layers.append(select_activation(self.activation))
            decoder_layers.append(nn.Dropout(self.dropout_rate))
        decoder_layers.append(nn.Linear(self.units[0], self.input_dim))
        decoder_layers.append(select_activation(self.activation))
        decoder_layers.append(nn.Dropout(self.dropout_rate))
        decoder = nn.Sequential(*decoder_layers)
        return decoder
    
    def build_out(self):
        out_layers = []
        fc_inshape = self.input_dim if self.ed_pyramid == 1 else self.units[-1]
        out_layers.append(nn.Linear(fc_inshape, 4))  # Output 4 classes
        out_layers = nn.Sequential(*out_layers)
        return out_layers

    def forward(self, x):
        encoded = self.encoder(x)
        if self.ed_pyramid == 1:
            decoded = self.decoder(encoded)
            out = self.fc_out(decoded)
        else:
            out = self.fc_out(encoded)
        return out
    
    def print_info(self):
        x = torch.randn(32, self.input_dim)
        print('in_dim:\t', x.shape)
        for i in range(len(self.encoder)):
            x = self.encoder[i](x)
            if i % 3 == 0:
                j = int(i / 3)
                print(f'encoder_{j}:\t', x.shape)
        if self.ed_pyramid == 1:
            for i in range(len(self.decoder)):
                x = self.decoder[i](x)
                if i % 3 == 0:
                    j = int(i / 3)
                    print(f'decoder_{j}:\t', x.shape)
        x = self.fc_out(x)
        print(f'out_dim:\t', x.shape)

def select_activation(activation):
    if activation == 'relu':
        choose_activation = nn.ReLU()
    elif activation == 'leaky_relu':
        choose_activation = nn.LeakyReLU()
    elif activation == 'relu':
        choose_activation = nn.relu()  # 确保选择relu激活函数
    else: 
        choose_activation = nn.Tanh()
    return choose_activation





if __name__ == '__main__':
    model = FCAE()
    print(model)
    batch_size = 1024
    window_size = 1024
    epochs = 50

    # 生成五个类别的标签，0到4
    train_x = torch.randn(batch_size, 2*window_size)
    train_y = torch.randint(0, 5, (batch_size,))

    optimizer = optim.Adam(model.parameters(), lr=0.01)
    criterion = nn.CrossEntropyLoss()

    for epoch in range(epochs):
        model.train()
        optimizer.zero_grad()
        x = train_x
        y = train_y
        train_outputs = model.forward(x)
        loss = criterion(train_outputs, y)
        loss.backward()
        optimizer.step()
        print(f'Epoch [{epoch+1}/{epochs}], Loss: {loss:.7f}')

