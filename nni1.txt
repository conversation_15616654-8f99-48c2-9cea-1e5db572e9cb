用 NNI
nnictl stop -p 8080
nnictl stop --all
nnictl create --config config.yml --port 8080
nnictl create --config /home/<USER>/fcnn/config.yml --port 8888
nnictl create --config /home/<USER>/project/Mamba/waveform_mamba/config.yml --port 8888


开启环境      echo $PATH         export PATH="$HOME/.local/bin:$PATH"              pipenv shell
看gpu资源     nvidia-smi    sudo watch -n 1 nvidia-smi


 
装库             pip install -i https://pypi.tuna.tsinghua.edu.cn/simple 
打开环境       echo $PATH       export PATH="$HOME/.local/bin:$PATH"     pipenv shell
运行程序       python /home/<USER>/project/Mamba/waveform_mamba/main.py


虚拟环境的库所在的路径         /home/<USER>/.local/share/virtualenvs/project-RTeN_psi/lib/python3.10/site-packages/




