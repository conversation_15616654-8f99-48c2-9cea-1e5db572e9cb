import os
import pandas as pd

def convert_value_to_millivolts(value):
    if isinstance(value, str):
        value = value.strip().lower().replace(' ', '')  # 移除所有空格并转换为小写
        if value.endswith('mv'):
            return float(value[:-2])
        elif value.endswith('uv'):
            return float(value[:-2]) / 1000.0
        elif value.endswith('nv'):
            return float(value[:-2]) / 1e6
        elif value.endswith('pv'):
            return float(value[:-2]) / 1e12
        elif value.endswith('fv'):
            return float(value[:-2]) / 1e15
        elif value.endswith('av'):
            return float(value[:-2]) / 1e18
        elif value.endswith('v'):
            return float(value[:-1]) * 1000.0
        else:
            raise ValueError(f"未知单位的值: {value}")
    else:
        return value

def process_file(file_path):
    # 读取CSV文件
    df = pd.read_csv(file_path)

    # 检查文件是否至少有三列
    if df.shape[1] < 3:
        print(f"文件 {file_path} 不包含至少三列")
        return

    # 将第二列和第三列的单位转换为毫伏
    try:
        df.iloc[:, 1] = df.iloc[:, 1].apply(convert_value_to_millivolts)
        df.iloc[:, 2] = df.iloc[:, 2].apply(convert_value_to_millivolts)
    except ValueError as e:
        print(f"文件 {file_path} 处理时出现错误: {e}")
        return

    # 保存修改后的数据到原始文件
    df.to_csv(file_path, index=False)
    print(f"已转换文件 {file_path} 中的单位为毫伏")

def main():
    target_folder = r"D:\RFICtest\fangzhenqijian\LMH6881\6881_4\LMH6881_ADS_AIO\LMH6881_wrk\random\a"

    # 检查目标文件夹是否存在
    if not os.path.isdir(target_folder):
        print("目标文件夹不存在")
        return

    # 遍历目标文件夹中的所有CSV文件
    for filename in os.listdir(target_folder):
        if filename.endswith(".csv"):
            file_path = os.path.join(target_folder, filename)
            process_file(file_path)

if __name__ == "__main__":
    main()
