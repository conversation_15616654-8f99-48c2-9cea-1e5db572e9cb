import os
import pandas as pd

# 定义单位转换函数
def convert_to_nsec(value, unit):
    try:
        num = float(value)
        if unit.strip() == 'sec':
            num *= 1e-9
        elif unit.strip() == 'fsec':
            num *= 1e-6
        elif unit.strip() == 'psec':
            num *= 1e-3
        elif unit.strip() == 'nsec':
            num *= 1
        elif unit.strip() == 'usec':
            num *= 1e3
        else:
            raise ValueError(f"Unknown unit: {unit}")
        
        return num
    
    except ValueError:
        raise ValueError(f"Could not convert '{value}' to float.")


# 设置目标文件夹路径
folder_path = r'D:\RFICtest\fangzhenqijian\LMH6881\6881_4\LMH6881_ADS_AIO\LMH6881_wrk\random\a'

# 遍历目标文件夹内的所有文件
for filename in os.listdir(folder_path):
    if filename.endswith('.csv'):
        file_path = os.path.join(folder_path, filename)
        
        # 读取CSV文件
        df = pd.read_csv(file_path)
        
        # 处理第一列数据的单位转换
        for index, row in df.iterrows():
            first_value = row.iloc[0]
            last_value = first_value.split()[-1]  # 获取最后一个单词，即单位
            
            try:
                # 提取数字部分和单位部分，并进行转换
                num = ' '.join(first_value.split()[:-1])
                converted_value = convert_to_nsec(num, last_value)
                
                # 更新 DataFrame 中的值
                df.iloc[index, 0] = converted_value
            
            except ValueError as e:
                print(f"Error processing {filename}, row {index + 1}: {str(e)}")
        
        # 保存更新后的 CSV 文件
        updated_file_path = os.path.join(folder_path, f"updated_{filename}")
        df.to_csv(updated_file_path, index=False)
        
        print(f"Successfully converted and saved: {updated_file_path}")
