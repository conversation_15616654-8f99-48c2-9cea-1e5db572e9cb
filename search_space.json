{"epochs": {"_type": "choice", "_value": [100]}, "batch_size": {"_type": "choice", "_value": [512]}, "max_lr": {"_type": "choice", "_value": [0.0001]}, "min_lr": {"_type": "choice", "_value": [0.0002]}, "loss_fun": {"_type": "choice", "_value": [9]}, "loss_mul": {"_type": "choice", "_value": [2, 5, 8, 10]}, "error_tolerance": {"_type": "choice", "_value": [0.05, 0.01, 0.002]}, "data_suiji_flage": {"_type": "choice", "_value": [1]}, "suiji_train_num": {"_type": "choice", "_value": [1024]}, "suiji_test_num": {"_type": "choice", "_value": [1024]}, "num_files": {"_type": "choice", "_value": [100]}, "duan_train_begin": {"_type": "choice", "_value": [[0]]}, "duan_test_begin": {"_type": "choice", "_value": [[10000]]}, "num_train_sample": {"_type": "choice", "_value": [1024]}, "num_test_sample": {"_type": "choice", "_value": [1024]}, "window_size": {"_type": "choice", "_value": [1024]}, "stage": {"_type": "choice", "_value": [[2, 2, 2, 2]]}, "units": {"_type": "choice", "_value": [[512, 256, 128, 64]]}, "stride": {"_type": "choice", "_value": [[2]]}, "model_type": {"_type": "choice", "_value": ["FCAE"]}, "ed_pyramid": {"_type": "choice", "_value": [1]}, "activation": {"_type": "choice", "_value": ["tanh"]}}