import os
import time
import nni
from tqdm import tqdm
import torch
import torch.nn as nn
from torch.optim.lr_scheduler import ExponentialLR
from model import FCAE, BasicBlock, FCAE_ResNet,SimpleNN, M3_Model, M5_Model
from utils.draw import draw_loss_lr
from utils.train_test import test_model, load_model, CustomLoss, check_max_index, accuracy

def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def train_model(params, train_loader, test_loader,
                checkpoint_path, model_name, model_number):
    
    epochs = params['epochs']; batch_size = params['batch_size']
    max_lr = (params['max_lr']*batch_size/512); min_lr = params['min_lr']; gamma = params['gamma']; att_epoch = params['att_epoch']
    train = params['train']; loss_fun = params['loss_fun']; loss_mul = params['loss_mul']

    window_size = params['window_size']; activation = params['activation']; model_name = params['model_type']
    units = params['units']; ed_pyramid = params['ed_pyramid']; error_tolerance = params['error_tolerance']
    block = BasicBlock; stride = params['stride']; stage = params['stage']

    os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
    os.environ["CUDA_VISIBLE_DEVICES"] = "1"
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    train_on_gpu = torch.cuda.is_available()
    
    if model_name == 'FCAE_ResNet':
        model = FCAE_ResNet(block, window_size, stage, stride, activation, train, ed_pyramid)
    elif model_name == 'FCAE':
        model = FCAE(window_size, units, activation, train, ed_pyramid)
    elif model_name == 'SimpleNN':
        model = SimpleNN(window_size, 4)
    elif model_name == 'M3':
        model = M3_Model()
    elif model_name == 'M5':
        model = M5_Model()
    else: raise ValueError('未定义的model')
    print('\n创建模型并初始化权重')
    #print('model:', model)

    # 1、mse  2、mae  3、mae.max  4、mse+a*mae.max^2  5、(y-predict-0.1)^2
    criterion = CustomLoss(loss_fun, error_tolerance, loss_mul)
    optimizer = torch.optim.Adam(model.parameters(), lr = max_lr, weight_decay = 0.0001)
    loss_min = float('inf'); val_loss_min = float('inf'); val_acc_max = float('inf')# 先定义 model、优化器、loss函数、loss值
    
    print('检查是否已经拥有该结构的模型')
    if os.path.exists(checkpoint_path): # 如果存在则 加载
        print('已经拥有该结构的模型')
        model, loss_fun, error_tolerance, loss_mul, optimizer, loss_min, val_loss_min
        criterion = CustomLoss(loss_fun, error_tolerance, loss_mul)
        print('加载之前的模型:', model_name + '_' + model_number)
        print(f'之前的损失 Loss: {loss_min:.6f}, val_Loss: {val_loss_min:.6f}','\n')
    
    else: # 不存在则 保存新创建的 model 
        print('未拥有该结构的模型')
        checkpoint = {
            'model': model.state_dict(),
            'optimizer': optimizer.state_dict(),
            'loss_fun': loss_fun,
            'error_tolerance': error_tolerance,
            'loss_mul': loss_mul,
            'loss_min': loss_min,
            'val_acc_max': val_acc_max,
        }  
        torch.save(checkpoint, checkpoint_path)  # 保存模型
        print('创建该模型并保存:', model_name + '_' + model_number,'\n')
    
    if train_on_gpu:
        print('在GPU上训练')
        if torch.cuda.device_count() > 1:  # 将模型放到多个GPU上
            model = nn.DataParallel(model)
        else: pass
        print('train using {} GPUs.'.format(torch.cuda.device_count()))
    else: print('在CPU上训练')

    model = model.to(device); model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr = max_lr, weight_decay = 0.0001)
    scheduler = ExponentialLR(optimizer, gamma = gamma)
    print(f"{model_name} 模型的参数数量为: {count_parameters(model)}")

    print('\n模型开始训练..........................')
    loss_list = []; val_loss_list = []; lr_list = []; total_time = 0
    val_acc_max = 0.0; acc_list = []; val_acc_list = []
    
    for epoch in range(epochs):
        start_time = time.time()
        loss_sum = 0.0; val_loss_sum = 0.0; acc_sum = 0.0; val_acc_sum = 0.0
        for Txy_data in tqdm(train_loader):  # 取一个 batch_size 的数据
            optimizer.zero_grad()
            x = Txy_data[1].view(-1, 2, window_size).float().to(device)  # (batch_size, 2, window_size)
            y = Txy_data[2].long().to(device) # (batch_size, 1)

            train_outputs = model.forward(x)  # (batch_size, num_error)
            loss = criterion(train_outputs, y)
            loss.backward()
            optimizer.step()
            loss_sum += loss
            acc_sum += accuracy(train_outputs, y)

        with torch.no_grad():
            for Txy_data in tqdm(test_loader):  # 取一个 batch_size 的数据
                x = Txy_data[1].view(-1, 2, window_size).float().to(device)  # (batch_size, 2, window_size)
                y = Txy_data[2].long().to(device) # (batch_size, 1)

                test_outputs = model.forward(x)  # (batch_size, num_error)
                val_loss = criterion(test_outputs, y)
                val_loss_sum += val_loss
                val_acc_sum += accuracy(test_outputs, y)

        acc_avg = acc_sum/len(train_loader); acc_list.append(acc_avg)
        val_acc_avg = val_acc_sum/len(test_loader); val_acc_list.append(val_acc_avg)
        loss_avg = (loss_sum/len(train_loader)).item(); loss_list.append(loss_avg)
        val_loss_avg = (val_loss_sum/len(test_loader)).item(); val_loss_list.append(val_loss_avg)
        nni.report_intermediate_result(acc_avg)

        if val_acc_avg > val_acc_max:       # 如果当前模型的验证集损失更低，则更新最佳模型
            val_acc_max = val_acc_avg
            loss_min = loss_avg
            checkpoint = {
                'model': model.state_dict(),
                'optimizer': optimizer.state_dict(),
                'loss_fun': loss_fun,
                'loss_mul': loss_mul,
                'error_tolerance': error_tolerance,
                'val_acc_max': val_acc_max,
            }  
            torch.save(checkpoint, checkpoint_path)  # 保存模型
            print('找到 val_loss 更小的模型并保存')

        if epoch % att_epoch == 0:  # ReduceLROnPlateau 需要 loss，
            scheduler.step()  # ExponentialLR, StepLR 不需要 loss
            if optimizer.param_groups[0]['lr'] < min_lr:
                optimizer.param_groups[0]['lr'] = min_lr  # 将学习率设置为最小值
                
        current_lr = optimizer.param_groups[0]['lr']
        lr_list.append(current_lr)           
        end_time = time.time()
        spend_time = end_time - start_time
        total_time += spend_time

        print(epoch, loss_avg, val_loss_avg, acc_avg, val_acc_avg)
        
    
    print('\n模型训练结束')
    print('本次训练的模型是：', model_name + '_' + model_number)
    draw_loss_lr(loss_list, val_loss_list, acc_list, val_acc_list, params)
