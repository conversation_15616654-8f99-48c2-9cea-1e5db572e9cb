import numpy as np
import random
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

def draw_loss_lr(loss_list, val_loss_list, acc_list, val_acc_list, params):
    epochs_list = list(range(1, params['epochs']+1))
    
    # 绘制 Loss 图像
    picture_name = 'LOSS.png'
    path = params['picture_save_path'] + '/' + picture_name
    fig, axs = plt.subplots()
    axs.plot(epochs_list, loss_list, color='orange', label='loss')
    axs.plot(epochs_list, val_loss_list, color='blue', label='val_loss')
    axs.set_title('LOSS')
    axs.set_xlabel('epoch')
    axs.set_ylabel('LOSS')
    axs.legend()
    plt.savefig(path)
    plt.close()  # 关闭当前图表，准备绘制下一张图
    
    # 绘制 Accuracy 图像
    picture_name = 'acc.png'
    path = params['picture_save_path'] + '/' + picture_name
    fig, axs = plt.subplots()
    axs.plot(epochs_list, acc_list, color='orange', label='acc')
    axs.plot(epochs_list, val_acc_list, color='blue', label='val_acc')
    axs.set_title('Accuracy')
    axs.set_xlabel('epoch')
    axs.set_ylabel('acc')
    axs.legend()
    plt.savefig(path)
    plt.close()  # 关闭当前图表，准备绘制下一张图

def display(acc_train, acc_test, loss_test, loss_train, model, crossVal):
    """显示训练和测试的平均损失和准确率"""
    plt.figure(figsize=(12, 7))
    plt.subplot(121)
    plt.plot(acc_train, label='训练准确率')
    plt.plot(acc_test, label='测试准确率')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.xscale('log')
    plt.legend()
    plt.title('模型' + ' 训练交叉验证 ' + str(crossVal))

    plt.subplot(122)
    plt.plot(loss_train, label='训练损失')
    plt.plot(loss_test, label='测试损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.xscale('log')
    plt.legend()
    
    plt.title('模型' + ' 测试交叉验证 ' + str(crossVal))
    plt.show()

def draw_waveform(tensor_data, orgin_data, params, train, train_on_gpu):
    if train:
        str_set = 'train set waveform'
        begin = params['duan_train_begin']
        segment_points = int(params['num_train_sample'] / len(begin))
        id_max = params['train_after_wave_n']
    else: 
        str_set = 'test set waveform'
        begin = params['duan_test_begin']
        segment_points = int(params['num_test_sample'] / len(begin))
        id_max = params['test_after_wave_n']

    if train_on_gpu:
        numpy_data = tensor_data.detach().cpu().numpy()
    else:
        numpy_data = tensor_data.detach().numpy()
    data_list = split_array_by_id(numpy_data, id_max)
    num_look = params['num_look']

    MSE = 0.0
    RF = 0.0

    for i in range(id_max):
        idl, t, y, pred_y = recovery_order(data_list[i])

        for j in range(len(begin)):
            start_index = j * segment_points
            end_index = min(start_index + num_look, len(y))
            t_j = t[start_index:end_index]
            y_j = y[start_index:end_index]
            pred_y_j = pred_y[start_index:end_index]

            MSE_temp, RF_temp = wucha(y_j.reshape(-1, 1), pred_y_j.reshape(-1, 1))
            MSE += float(MSE_temp)
            RF += float(RF_temp)

            picture_name = 'pred-' + str_set + '-' + str(i) + 'segment-' + str(j) + '.png'
            path = params['picture_save_path'] + '/' + picture_name
            fig, ax = plt.subplots()
            ax.plot(t_j, y_j, color='orange', label='y(t)')
            ax.plot(t_j, pred_y_j, color='blue', label='pred_y(t)')
            ax.set_title(str_set + ' ' + str(idl) + ': mse=' + str(MSE_temp))
            ax.set_xlabel('t')
            ax.set_ylabel('Value')
            ax.legend()
            plt.savefig(path)
            plt.close()  # 关闭当前图表，准备绘制下一张图

    MSE = "{:.6f}".format(MSE / id_max / len(begin))
    RF = "{:.4f}".format(RF / id_max / len(begin))
    print(str_set, ':', 'MSE=', MSE, 'R2=', RF)

def split_array_by_id(arr, id_max):
    id_set = set(range(id_max))
    result = [[] for _ in range(id_max)]
    for row in arr:
        if row[0] in id_set:
            first_col_value = int(row[0])
            result[first_col_value].append(row)
    return result

def recovery_order(data_list):
    numpy_data = np.array(data_list)
    sort_indices = np.argsort(numpy_data[:, 1])
    sorted_data = numpy_data[sort_indices]
    t = sorted_data[:, 1]
    y = sorted_data[:, 2]
    pred_y = sorted_data[:, 3]
    idl = int(numpy_data[0, 0])
    return idl, t, y, pred_y

def data_visualization(data, train, params):
    N = int(data.shape[0] / 3)
    num_look = params['num_look']

    if train:
        str_jihe = 'train set waveform'
        n = params['train_per_wave_n']
    else:
        str_jihe = 'test set waveform'
        n = params['test_per_wave_n']

    if n > N:
        n = N

    look_start = random.randint(0, data.shape[1] - num_look + 1)
    data = data[:, look_start:look_start + num_look]
    print('data:', data.shape)
    print('观察 ' + str_jihe + ' ' + str(data.shape[1]) + ' 个点')

    for i in range(0, n):
        picture_name = str_jihe + '_' + str(i) + '.png'
        path = params['picture_save_path'] + '/' + picture_name
        fig, axs = plt.subplots()
        axs.plot(data[3 * i], data[3 * i + 1], color='blue', label='x' + str(i) + '(t)')
        axs.plot(data[3 * i], data[3 * i + 2], color='orange', label='y' + str(i) + '(t)')
        axs.set_title(str_jihe + '_' + str(i))
        axs.set_xlabel('t')
        axs.set_ylabel('Value')
        axs.legend()
        plt.savefig(path)
        plt.close()  # 关闭当前图表，准备绘制下一张图

def wucha(y_zhenzhi, y_predictions):
    length = y_zhenzhi.shape[0]
    MSE = (1 / length) * np.dot((y_zhenzhi - y_predictions).T, (y_zhenzhi - y_predictions))
    MSE = "{:.6f}".format(MSE[0][0])

    y_pingjun = (1 / length) * np.sum(y_zhenzhi)
    y_pingjun = np.multiply(y_pingjun, np.ones(((length), 1)))
    RF = 1 - (np.dot((y_zhenzhi - y_predictions).T, (y_zhenzhi - y_predictions)) / np.dot((y_zhenzhi - y_pingjun).T, (y_zhenzhi - y_pingjun)))
    RF = "{:.6f}".format(RF[0][0])
    return MSE, RF

def combine_waveform_images(data, params):
    fig, ax = plt.subplots(figsize=(12, 7))  # 创建一个图表

    colors = ['blue', 'green', 'red', 'purple']
    x_labels = ['normal_input', 'MOS_fault_input', 'output_high_input', 'output_low_input']
    y_labels = ['normal_output', 'MOS_fault_output', 'output_high_output', 'output_low_output']

    N = int(data.shape[0] / 3)
    num_look = params['num_look']
    n = params['test_per_wave_n']

    if n > N:
        n = N

    look_start = random.randint(0, data.shape[1] - num_look + 1)
    data = data[:, look_start:look_start + num_look]

    for i in range(n):
        if i == 1:  # Skip x1(t) and y1(t)
            continue

        t = data[3 * i]
        x_t = data[3 * i + 1]
        y_t = data[3 * i + 2]

        if i < len(colors):
            ax.plot(t, x_t, color=colors[i], label=x_labels[i] if x_labels[i] else None)
            ax.plot(t, y_t, linestyle='--', color=colors[i], label=y_labels[i] if y_labels[i] else None)

    ax.set_title('Combined Waveforms')
    ax.set_xlabel('t')
    ax.set_ylabel('Value')
    ax.legend()

    combined_picture_name = 'combined_test_set_waveforms.png'
    combined_path = params['picture_save_path'] + '/' + combined_picture_name
    plt.savefig(combined_path)
    plt.close()  # 关闭当前图表，准备绘制下一张图



