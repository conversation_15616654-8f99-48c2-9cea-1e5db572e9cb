import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import FixedFormatter
from scipy.interpolate import interp2d, LinearNDInterpolator
import matplotlib.colors as colors
import matplotlib.cm as cm

# 创建数据
x_arrays = np.array([[4, 5, 6, 7, 8]])
y_arrays = np.array([512, 1024, 2048, 4096, 8192])
z_values = np.array([[0.000127, 0.000244, 0.000464, 0.002004, 0.000602], [0.002361, 0.003948, 0.000221, 0.000578, 0.000859], 
                     [0.000395, 0.000234, 0.000439, 0.000132, 0.000464], [0.001318, 0.000363, 0.000459, 0.003832, 0.000221], 
                     [0.0169, 0.0130, 0.0183, 0.0099, 0.0190]])

x_values = [int(x) for x in range(0, 29, 2)]
network_structures = {
    0: '0.5k_4',
    2: '0.5k_5',
    4: '0.5k_6',
    6: '1k_5',
    8: '1k_6',
    10: '1k_7',
    12: '2k_6',
    14: '2k_7',
    16: '2k_8',
    18: '4k_7',
    20: '4k_8',
    22: '4k_9',
    24: '8k_8',
    26: '8k_9',
    28: '8k_10',
}

network_layers = np.arange(0, 29, 2)
window_size = [2 ** i for i in range(7, 14)]

test_mse_800MHz = [[0.001747, 0.000545, 0.001159, 0.001698, 0.001506], ]
test_mse_1GHz = np.array([
                 [0.000359, 0.000461, 0.000219, 0.000359, 0.000242, 0.000233, 0.000117],    #                network_structure(col)
                 [0.000145, 0.000178, 0.000170, 0.000200, 0.000244, 0.000208, 0.000222],    #window size(row)
                 [0.000127, 0.000264, 0.000207, 0.000324, 0.000093, 0.000367, 0.000142], 
                 [0.000244, 0.000243, 0.000347, 0.000474, 0.000528, 0.000608, 0.000320], 
                 [0.000464, 0.000266, 0.000309, 0.000371, 0.000699, 0.002000, 0.000139], 
                 [0.002004, 0.000493, 0.000501, 0.000367, 0.000818, 0.000425, 0.002164], 
                 [0.000602, 0.001624, 0.001279, 0.000954, 0.002121, 0.001846, 0.002180]
                 ])

#                         network_layers
#                           units[512, 64]     [512, 32]     [512, 16]     [1024, 64]     [1024, 32]     [2048, 64]     [2048, 32]      [2048, 16]       
#   window_size 2048
#               4096
#               8192
general_window_size = np.array([int(2 ** i) for i in range(11, 14)])
test_mse_500MHz_1GHz = np.array([
                 [0.00195, 0.00205, 0.001974, 0.002526, 0.002576, 0.001784, 0.001643, 0.002467, 0.001978, 0.001732, 0.001661, 0.002477, 0.001642, 0.002366, 0.001964],
                 [0.004683, 0.002086, 0.00273, 0.002292, 0.001511, 0.004085, 0.002465, 0.002261, 0.002199, 0.00224, 0.002633, 0.00121, 0.002079, 0.00174, 0.008],       #0.801475
                 [0.007824, 0.00426, 0.004541, 0.002815, 0.003636, 0.003161, 0.001478, 0.002789, 0.00192, 0.002448, 0.002799, 0.002188, 0.002689, 0.001418, 0.009],      #0.07629
])

print(np.min(test_mse_500MHz_1GHz))

# 创建插值函数
f = interp2d(network_layers, general_window_size, test_mse_500MHz_1GHz, kind='linear')

# 创建插值数据
layers_interp = np.linspace(min(network_layers), max(network_layers), 100)
window_sizes_interp = np.linspace(min(general_window_size), max(general_window_size), 100)

test_mse_interp = f(layers_interp, window_sizes_interp)
# 绘制曲面
X, Y = np.meshgrid(layers_interp, window_sizes_interp)     #  要先拼接成网格数据

# 创建三维图形对象
color_norm = colors.Normalize(vmin=0, vmax=0.005)

boundaries = np.linspace(0, 0.005, 21)

cmap = cm.get_cmap('inferno')
cmap.set_over('yellow')

fig = plt.figure(figsize=(15, 12))
ax = fig.add_subplot(111, projection='3d')



# 绘制三维曲面图
surface = ax.plot_surface(X, Y, test_mse_interp, cmap='inferno', norm = color_norm)

# 标注mse最小值
min_mse_index = np.unravel_index(np.argmin(test_mse_500MHz_1GHz), test_mse_500MHz_1GHz.shape)
min_mse_value = test_mse_500MHz_1GHz[min_mse_index]
# ax.scatter(network_layers[min_mse_index[1]], general_window_size[min_mse_index[0]], min_mse_value, color='blue', s=100)
# ax.text(network_layers[min_mse_index[1]], general_window_size[min_mse_index[0]], min_mse_value, f'Min MSE: {min_mse_value:.5f}', color='red')

# 在图像外部创建文本框，将最小值信息显示在文本框中
textbox_text = f'Min MSE: {min_mse_value:.5f}\nNetwork Structure: {network_structures[network_layers[min_mse_index[1]]]}\nWindow Size: {general_window_size[min_mse_index[0]]}'
fig.text(0.5, 0.01, textbox_text, ha='center', fontsize=12)

# 设置坐标轴标签
ax.set_xlabel('network layers', labelpad = 50)
# ax.xaxis.set_major_locator(ticker.MultipleLocator(1))
ax.xaxis.set_major_formatter(FixedFormatter([network_structures[val] for val in x_values]))
ax.set_xticks(x_values)
ax.set_ylabel('window size', labelpad = 20, rotation = 45)
ax.set_zlabel('test mse', labelpad = 20)

# ax.zaxis.set_major_formatter(plt.FuncFormatter(lambda z, _: '{:.0e}'.format(z)))

#  趋势图颜色对比

cbar = fig.colorbar(surface, boundaries=boundaries, ticks = boundaries)
cbar.set_label('Test Mse Color Map')
# 显示图形
plt.show()