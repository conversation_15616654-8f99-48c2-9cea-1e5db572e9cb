import numpy as np
import time
import os
import torch

def jisuan_delay(data, N, L, train): # 将输入 data 每三列为 1 组计算延时
    delay = []
    if train == True:  # 训练集统一计算延时
        numpy_x = data[:, 1].flatten(); numpy_y = data[:, 2].flatten()  # 取一组波形来计算延时
        corr = np.correlate(numpy_x, numpy_y, mode='full')
        delay.append(-(np.argmax(corr)-(len(numpy_x)-1))) 
    else:              # 测试集分别计算延时
        for i in range(N):
            numpy_x = data[:, 1+(3*i):2+(3*i)].flatten(); numpy_y = data[:, 2+(3*i):3+(3*i)].flatten()
            corr = np.correlate(numpy_x, numpy_y, mode='full')
            delay.append(-(np.argmax(corr)-(len(numpy_x)-1))) 
    delay = [np.abs(x) for x in delay]
    # print(delay)
    return delay
def quchu_delay(data, N, L, train, delay):
    if delay[0] != 0:      # delay 不为 0 才进行去除延时
        remove_delay_data = np.zeros((L, (3*N)))    # 创建数组来放去除延时后的数据
        if train == True:  # 训练集统一去除延时
            if delay[0] != 0:  # 为防止 delay = 0 而报错才分的 if else
                remove_delay_data[:-delay[-1], ::3] = data[:-delay[-1], 0::3]  # 把 data 的 T 给 remove_delay_data
                remove_delay_data[:-delay[-1], 1::3] = data[:-delay[-1], 1::3] # 把 data 的 X 给 remove_delay_data
                remove_delay_data[:-delay[-1], 2::3] = data[delay[-1]:, 2::3]  # 把 data 的 Y 给 remove_delay_data
                return remove_delay_data[:-delay[-1], :]
            else: return data
        else:               # 测试集分别去除延时
            for i in range(N):
                if delay[i] != 0:  # 为防止 delay[i] = 0 而报错才分的 if else
                    remove_delay_data[:-delay[i], 0+(3*i):1+(3*i)] = data[:-delay[i], 0+(3*i):1+(3*i)]  # 把 data 的 T 给 remove_delay_data
                    remove_delay_data[:-delay[i], 1+(3*i):2+(3*i)] = data[:-delay[i], 1+(3*i):2+(3*i)] # 把 data 的 X 给 remove_delay_data
                    remove_delay_data[:-delay[i], 2+(3*i):3+(3*i)] = data[delay[i]:, 2+(3*i):3+(3*i)]  # 把 data 的 Y 给 remove_delay_data
                else:
                    remove_delay_data[:, 0+(3*i):1+(3*i)] = data[:, 0+(3*i):1+(3*i)]  # 把 data 的 T 给 remove_delay_data
                    remove_delay_data[:, 1+(3*i):2+(3*i)] = data[:, 1+(3*i):2+(3*i)] # 把 data 的 X 给 remove_delay_data
                    remove_delay_data[:, 2+(3*i):3+(3*i)] = data[:, 2+(3*i):3+(3*i)]  # 把 data 的 Y 给 remove_delay_data                        
            remove_delay_data = remove_delay_data[:-np.max(delay), :] # 将数组的每列从尾部按最短的数组长度截断
            return remove_delay_data
    else: return data # delay 为 0 直接返回数据本身
    

def normalize(data, N, normalize_value=None):
    normalization_data = np.zeros_like(data)
    x_col = list(range(1, 3*N, 3)); y_col = list(range(2, 3*N, 3))
    x_data = data[:, x_col]; y_data = data[:, y_col]  # 取出所选列的数据
    x_max = np.max(x_data); y_max = np.max(y_data)  # 计算所有数据中的最大和最小值
    x_min = np.min(x_data); y_min = np.min(y_data)

    if normalize_value is None:
        normalize_value = [x_min, x_max, y_min, y_max]
    else: pass
    abs_norm_value = np.abs(normalize_value)

    normalization_data[:, ::3] = data[:, ::3]  # T 列不进行归一化
    normalization_data[:, 1::3] = data[:, 1::3]/abs_norm_value[1]
    normalization_data[:, 2::3] = data[:, 2::3]/abs_norm_value[3]

    return normalization_data, abs_norm_value

def data_normalization(data, train, params, normalize_value=None):      # T1,X1,Y1  T2,X2,Y2, ...... ,  T10,X10,Y10
    data = data.values  # DataFram 转化为矩阵
    N = int(data.shape[1]/3) # 几条波形
    L = data.shape[0]
    normal_flage = params['normal_flage']; yanchi_flage = params['yanchi_flage']; delay_known = params['delay_known']

    if yanchi_flage: # 需要修正延迟
        if delay_known == False:  # 延迟未知
            delay = jisuan_delay(data, N, L, train)
            remove_delay_data = quchu_delay(data, N, L, train, delay)
        else:                     # 延迟已知
            if train == True:    # 确定延时多少个点
                delay = params['train_delay']
            else: delay = params['test_delay']
            remove_delay_data = quchu_delay(data, N, L, train, delay)
    else:   # 不需要修正延迟
        remove_delay_data = data

    normalized_value = None
    if normal_flage:
        normalization_data, normalized_value = normalize(remove_delay_data, N, normalize_value=normalize_value) # 数据统一归一化处理
        return normalization_data.T, normalized_value
    else:
        return remove_delay_data.T, normalized_value
