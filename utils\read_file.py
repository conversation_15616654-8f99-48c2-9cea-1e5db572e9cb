import os
import pandas as pd
import natsort

def read_csv_file_name_list(folder_path, train, num_files=None): # 读取 folder_path 下的所有 csv 文件的名称，形成列表
    file_list = os.listdir(folder_path)   # 列出文件夹下的所有文件
    sort_list = natsort.natsorted(file_list)

    csv_file_name_list = []  # 存储 csv 文件路径的列表
    count = 0
    for file_name in sort_list:
        if file_name.endswith('.csv'):  # 判断文件扩展名是否为csv
            file_path = os.path.join(folder_path, file_name)  # 拼接文件路径
            csv_file_name_list.append(file_path)
            count += 1
            if num_files is not None and count >= num_files:  # 控制取出文件的数量
                break
    if train:
        str_set = '训练集'
    else: str_set = '测试集'
    print('选取'+str(len(csv_file_name_list))+'条曲线做'+str_set)
    # print(file_path)
    return csv_file_name_list

def read_merged_data(csv_file_names, train, max_rows=200000):  # 整合 csv 文件的数据，返回 DataFrame 对象的数据，波形条数，波形点数
    df_list = []
    for file in csv_file_names:   # 逐个读取每个CSV文件
        df = pd.read_csv(file, usecols=[0, 1, 2], nrows=max_rows)
        # print(f'Reading file: {file}')  # 在这里对每个文件进行相应的处理，例如输出文件名
        df_list.append(df)
    merged_df = pd.concat(df_list, axis=1)  # 整合成一个 DataFrame 对象
    return merged_df

    if train:
        str_train = 'train'
    else: str_train = 'test'
    print(str_train + ' DataFram 读取完成')
    return merged_df
