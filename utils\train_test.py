import os
import torch
import torch.nn as nn
from utils.draw import draw_waveform
from model import FCAE_ResNet, BasicBlock, FCAE, SimpleNN, M3_Model, M5_Model
from dataloader import duan_Dataset, create_dataloader
import numpy as np
from sklearn.metrics import recall_score, f1_score

class CustomLoss(nn.Module):
    def __init__(self, loss_fun, error_tolerance, loss_mul):
        super(CustomLoss, self).__init__()
        self.loss_fun = loss_fun
        self.loss_mul = loss_mul
        self.error_tolerance = error_tolerance
    
    def forward(self, pred_y, y):
        if self.loss_fun == 1:
            loss = torch.mean((pred_y - y)**2)
        elif self.loss_fun == 2:
            loss = torch.mean(torch.abs(pred_y - y))
        elif self.loss_fun == 3:
            loss = torch.max(torch.mean(torch.abs(pred_y - y)))
        elif self.loss_fun == 4:
            mse = torch.mean((pred_y - y)**2)
            mae_max = torch.max(torch.mean(torch.abs(pred_y - y)))
            loss = mse + self.loss_mul * mae_max**2
        elif self.loss_fun == 5:
            loss = torch.mean((pred_y - y + self.loss_mul)**2)
        elif self.loss_fun == 6:
            loss = torch.mean((pred_y - y - self.loss_mul)**2)
        elif self.loss_fun == 7:
            loss = torch.mean(((1 + self.loss_mul) * pred_y - y)**2)
        elif self.loss_fun == 8:
            loss = torch.mean(((1 - self.loss_mul) * pred_y - y)**2)
        elif self.loss_fun == 9:
            abs_error = torch.abs(pred_y - y)
            weight = torch.where(abs_error > self.error_tolerance, self.loss_mul, 1)
            loss = torch.mean(weight * (pred_y - y)**2)
        elif self.loss_fun == 10:
            criterion = torch.nn.CrossEntropyLoss()
            loss = criterion(pred_y, y)
        else:
            loss = torch.mean((pred_y - y)**2)
        return loss

def load_model(model, optimizer, checkpoint_path):
    checkpoint = torch.load(checkpoint_path)
    state_dict = checkpoint['model']
    if 'module' in list(state_dict.keys())[0]:
        state_dict = {k[7:]: v for k, v in state_dict.items()}
    model.load_state_dict(state_dict)
    optimizer.load_state_dict(checkpoint['optimizer'])
    val_acc_max = checkpoint['val_acc_max']
    return model, optimizer, val_acc_max

def test_model(loader, model, window_size, device, data_acquisition_flage=False):
    model.eval()
    test_tensor = torch.empty(0, 6).to(device)
    test_criterion = CustomLoss(10, 0.05, 0.1)

    all_targets = []
    all_predictions = []

    with torch.no_grad():
        test_loss = 0.0; test_loss_sum = 0.0
        for Txy_data in loader:
            T = Txy_data[0].view(-1, 1).float().to(device)
            x = Txy_data[1].view(-1, 2, window_size).float().to(device)
            y = Txy_data[2].long().to(device)
            idl = Txy_data[3].view(-1, 1).to(device)

            test_outputs = model.forward(x)
            test_loss = test_criterion(test_outputs, y)
            test_loss_sum += test_loss

            if data_acquisition_flage:
                test_one_batch_data = torch.cat((idl, y.unsqueeze(1), test_outputs), dim=1)
                test_tensor = torch.cat((test_tensor, test_one_batch_data), dim=0)

            all_targets.extend(y.cpu().numpy())
            all_predictions.extend(torch.argmax(test_outputs, dim=1).cpu().numpy())
                
        test_loss_avg = (test_loss_sum / len(loader)).item()

        # 计算召回率和F1分数
        recall = recall_score(all_targets, all_predictions, average='macro')
        f1 = f1_score(all_targets, all_predictions, average='macro')

        print(f'Recall: {recall}')
        print(f'F1 Score: {f1}')

        return test_loss_avg, test_tensor

def accuracy(output, target):
    batch_size = target.size(0)
    _, predicted = torch.max(output.data, 1)
    acc = (predicted == target).sum().item() / batch_size
    return acc

def model_effect_visualization(loader, data, model, device, train_on_gpu, train, params):
    window_size = params['window_size']
    loss_fun = params['loss_fun']
    loss_mul = params['loss_mul']
    criterion = CustomLoss(loss_fun, 0.01, loss_mul)
    
    all_targets = []
    all_predictions = []

    with torch.no_grad():
        test_loss_sum = 0.0; test_acc_sum = 0.0
        for Txy_data in loader:
            x = Txy_data[1].view(-1, 2, window_size).float().to(device)
            y = Txy_data[2].long().to(device)

            test_outputs = model.forward(x)
            val_loss = criterion(test_outputs, y)
            test_loss_sum += val_loss
            test_acc_sum += accuracy(test_outputs, y)

            all_targets.extend(y.cpu().numpy())
            all_predictions.extend(torch.argmax(test_outputs, dim=1).cpu().numpy())

    acc = test_acc_sum / len(loader)
    print('acc:', acc)

    # 计算召回率和F1分数
    recall = recall_score(all_targets, all_predictions, average='macro')
    f1 = f1_score(all_targets, all_predictions, average='macro')

    print(f'Recall: {recall}')
    print(f'F1 Score: {f1}')

def check_max_index(test_outputs, params, train):
    y = test_outputs[:, :4]
    pred_y = test_outputs[:, 4:]
    max_idx1 = torch.argmax(y, dim=1)
    max_idx2 = torch.argmax(pred_y, dim=1)
    acc = accuracy(pred_y, max_idx1)
    return acc

def test_best_model(checkpoint_path, train_data, test_data, model_name, model_number, params):
    train = params['train']; window_size = params['window_size']; batch_size = params['batch_size']
    window_size = params['window_size']; activation = params['activation']
    units = params['units']; block = BasicBlock; stride = params['stride']
    ed_pyramid = params['ed_pyramid']; stage = params['stage']
    loss_fun = params['loss_fun']; loss_mul = params['loss_mul']; error_tolerance = params['error_tolerance']
    os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
    os.environ["CUDA_VISIBLE_DEVICES"] = "1"
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    train_on_gpu = torch.cuda.is_available()
    
    if model_name == 'FCAE_ResNet':
        model = FCAE_ResNet(block, window_size, stage, stride, activation, not train, ed_pyramid)
    elif model_name == 'FCAE':
        model = FCAE(window_size, units, activation, not train, ed_pyramid)
    elif model_name == 'SimpleNN':
        model = SimpleNN(window_size, 5)
    elif model_name == 'M3':
        model = M3_Model()
    elif model_name == 'M5':
        model = M5_Model()
    else:
        raise ValueError('未定义的model')
    
    print('\ntesting................................')
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=0.0001)
    val_acc_max = float('inf')

    model, optimizer, val_acc_max = load_model(model, optimizer, checkpoint_path)

    if torch.cuda.device_count() > 1:
        model = nn.DataParallel(model)
    print('test using {} GPUs.'.format(torch.cuda.device_count()))
    model = model.to(device)
    
    train_normalize_value = params.get('train_normalize_value', None)
    train_dataset = duan_Dataset(train_data, params, params['train'], None)
    test_dataset = duan_Dataset(test_data, params, not params['train'], normalize_value=train_normalize_value)
    train_loader = create_dataloader(train_dataset, train, batch_size)
    test_loader = create_dataloader(test_dataset, not train, batch_size)

    print('\n在 best_model 上看效果')
    model_effect_visualization(test_loader, test_data, model, device, train_on_gpu, not train, params)
    #model_effect_visualization(train_loader, train_data, model, device, train_on_gpu, train, params)
